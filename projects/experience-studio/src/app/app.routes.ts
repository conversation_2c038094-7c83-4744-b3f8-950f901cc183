import { Routes } from '@angular/router';
import { PromptSubmissionGuard } from './shared/guards/prompt-submission.guard';
import { CardSelectionGuard } from './shared/guards/card-selection.guard';

export const routes: Routes = [
  // Main landing page route
  {
    path: '',
    loadComponent: () =>
      import('./shared/components/landing-page/landing-page.component').then(
        m => m.LandingPageComponent
      ),
    data: { preload: true }
  },

  // Project routes - for both generation and opening recent projects
  {
    path: 'projects/:projectId',
    loadComponent: () =>
      import('./shared/components/code-window/code-window.component').then(
        m => m.CodeWindowComponent
      ),
    data: {
      flowType: 'project-loading',
      isProjectLoading: true,
      skipGuards: true
    }
  },

  // Generate Application flow routes
  {
    path: 'generate-application',
    children: [
      {
        path: 'prompt',
        loadComponent: () =>
          import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
            m => m.PromptContentComponent
          ),
        canActivate: [CardSelectionGuard],
        data: {
          flowType: 'generate-application',
          cardType: 'Generate Application'
        }
      },
      {
        path: 'code-preview',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        canActivate: [PromptSubmissionGuard],
        data: {
          flowType: 'generate-application',
          cardType: 'Generate Application'
        }
      },
      {
        path: 'code-preview/projects/:projectId',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        data: {
          flowType: 'generate-application',
          cardType: 'Generate Application',
          isProjectLoading: true,
          skipGuards: true
        }
      },
      {
        path: '',
        redirectTo: 'prompt',
        pathMatch: 'full'
      }
    ]
  },

  // Generate UI Design flow routes
  {
    path: 'generate-ui-design',
    children: [
      {
        path: 'prompt',
        loadComponent: () =>
          import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
            m => m.PromptContentComponent
          ),
        canActivate: [CardSelectionGuard],
        data: {
          flowType: 'generate-ui-design',
          cardType: 'Generate UI Design'
        }
      },
      {
        path: 'code-preview',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        canActivate: [PromptSubmissionGuard],
        data: {
          flowType: 'generate-ui-design',
          cardType: 'Generate UI Design'
        }
      },
      {
        path: '',
        redirectTo: 'prompt',
        pathMatch: 'full'
      }
    ]
  },
  // Legacy routes for backward compatibility
  {
    path: 'experience',
    children: [
      {
        path: '',
        loadChildren: () => import('./pages/experience-routing').then(m => m.EXPERIENCE_ROUTES),
        data: { preload: true }
      },
      // Legacy route - keep for backward compatibility
      {
        path: 'prompt',
        loadComponent: () =>
          import('./pages/image-to-code/components/prompt-content/prompt-content.component').then(
            m => m.PromptContentComponent
          ),
      },
      // Legacy route - keep for backward compatibility
      {
        path: 'code-preview',
        loadComponent: () =>
          import('./shared/components/code-window/code-window.component').then(
            m => m.CodeWindowComponent
          ),
        canActivate: [PromptSubmissionGuard],
      },
      // Legacy nested routes
      {
        path: 'generate-application',
        children: [
          {
            path: 'prompt',
            redirectTo: '/generate-application/prompt',
            pathMatch: 'full'
          },
          {
            path: 'code-preview',
            redirectTo: '/generate-application/code-preview',
            pathMatch: 'full'
          },
          {
            path: 'code-preview/projects/:projectId',
            redirectTo: '/projects/:projectId',
            pathMatch: 'full'
          }
        ]
      },
      {
        path: 'generate-ui-design',
        children: [
          {
            path: 'prompt',
            redirectTo: '/generate-ui-design/prompt',
            pathMatch: 'full'
          },
          {
            path: 'code-preview',
            redirectTo: '/generate-ui-design/code-preview',
            pathMatch: 'full'
          }
        ]
      }
    ]
  },

  // Demo routes for development
  {
    path: 'project-loading-demo',
    loadComponent: () =>
      import('./shared/components/project-loading-demo/project-loading-demo.component').then(
        m => m.ProjectLoadingDemoComponent
      ),
    data: {
      flowType: 'project-loading',
      skipGuards: true
    }
  },
  {
    path: 'demo-nav',
    loadComponent: () =>
      import('./shared/components/project-loading-demo/demo-navigation.component').then(
        m => m.DemoNavigationComponent
      ),
    data: {
      flowType: 'main',
      skipGuards: true
    }
  },

  // Wildcard route - redirect to main
  {
    path: '**',
    redirectTo: '',
    pathMatch: 'full'
  }
];
