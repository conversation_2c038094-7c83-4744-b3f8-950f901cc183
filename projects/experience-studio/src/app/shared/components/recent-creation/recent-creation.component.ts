import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy, CUSTOM_ELEMENTS_SCHEMA, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { HeadingComponent, BodyTextComponent, CardsComponent } from '@awe/play-comp-library';
import {
  RecentProjectService,
  Project,
  ProjectDetail,
  ProjectDetailResponse,
} from '../../services/recent-project-services/recent-project.service';
import { Router, NavigationEnd } from '@angular/router';
import { filter, switchMap } from 'rxjs/operators';
import { CardOption } from '../../models/recent-creation.model';
import { SubscriptionManager, ObserverManager } from '../../utils/subscription-management.util';
import { ToastService } from '../../services/toast.service';
import { HealthCheckService, HealthCheckResponse } from '../../services/health-check.service';
import { ProjectNavigationService } from '../../services/project-navigation.service';

@Component({
  selector: 'app-recent-creation',
  imports: [CommonModule, HeadingComponent, BodyTextComponent, CardsComponent],
  standalone: true,
  templateUrl: './recent-creation.component.html',
  styleUrl: './recent-creation.component.scss',
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RecentCreationComponent implements OnInit, OnDestroy {
  theme: 'light' | 'dark' = 'light';
  selectedId: string | null = null;
  currentCategory: string = 'recent';
  isLoading: boolean = true;
  hasError: boolean = false;
  options: { [category: string]: CardOption[] } = { recent: [], all: [] };

  // Project detail loading states
  private projectDetailLoadingStates = new Map<string, boolean>();
  private projectDetailErrors = new Map<string, string>();

  // Health check loading states for individual cards
  private healthCheckLoadingStates = new Map<string, boolean>();
  private healthCheckErrors = new Map<string, string>();

  // Hover and preview states
  hoveredCardId: string | null = null;
  private previewLoadingStates = new Map<string, boolean>();

  private subscriptionManager = new SubscriptionManager();
  private observerManager = new ObserverManager();

  // Flag to prevent duplicate API calls from multiple triggers
  private projectsLoaded = false;
  private isLoadingProjects = false;

  constructor(
    private recentProjectService: RecentProjectService,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private toastService: ToastService,
    private healthCheckService: HealthCheckService,
    private projectNavigationService: ProjectNavigationService
  ) {}

  ngOnInit(): void {
    this.initializePlaceholders();
    this.setupIntersectionObserver();

    // Load projects immediately if we're already on the landing page
    if (this.isOnLandingPage()) {
      this.loadProjectsIfNeeded();
    }

    // Set up subscription to load projects when navigating to the landing page
    // Only load if not already loaded to prevent duplicate API calls
    this.subscriptionManager.subscribe(
      this.router.events.pipe(filter(event => event instanceof NavigationEnd)),
      () => {
        if (this.isOnLandingPage()) {
          this.loadProjectsIfNeeded();
        }
      }
    );
  }

  ngOnDestroy(): void {
    // Clean up observers
    this.observerManager.cleanup();

    // Clean up project detail states
    this.projectDetailLoadingStates.clear();
    this.projectDetailErrors.clear();

    // Clean up health check states
    this.healthCheckLoadingStates.clear();
    this.healthCheckErrors.clear();

    // Reset flags for potential future use
    this.projectsLoaded = false;
    this.isLoadingProjects = false;
  }

  switchCategory(category: string): void {
    if (this.currentCategory !== category) {
      this.currentCategory = category;
      this.animateCategorySwitch(category);
      this.cdr.markForCheck(); // Trigger change detection
    }
  }

  getCurrentOptions(): CardOption[] {
    return this.options[this.currentCategory] || [];
  }

  /**
   * Handle selection of a project card
   * @param id The project ID that was selected
   * @param event Optional click event
   */
  handleSelection(id: string, event?: Event): void {
    if (event) event.preventDefault();

    // Skip if this is a placeholder card
    if (id.startsWith('placeholder-')) {
      return;
    }

    // Skip if health check is already loading for this card
    if (this.healthCheckLoadingStates.get(id)) {
      return;
    }

    // Set selected state
    this.selectedId = id;

    // Start health check first, then fetch project details
    this.performHealthCheckForCard(id);

    this.cdr.markForCheck(); // Trigger change detection
  }

  /**
   * Perform health check for a specific card
   * @param projectId The project ID to perform health check for
   */
  private performHealthCheckForCard(projectId: string): void {
    // Set health check loading state
    this.healthCheckLoadingStates.set(projectId, true);
    this.healthCheckErrors.delete(projectId); // Clear any previous errors

    // Perform health check
    this.subscriptionManager.subscribe(
      this.healthCheckService.performHealthCheck().pipe(
        switchMap((healthResponse: HealthCheckResponse) => {
          if (healthResponse.status === 'ok') {
            // Health check successful, proceed with project details
            return this.recentProjectService.getProjectById(projectId);
          } else {
            // Health check failed, throw error
            throw new Error(healthResponse.message || 'Health check failed');
          }
        })
      ),
      (response: ProjectDetailResponse) => this.handleHealthCheckAndProjectSuccess(projectId, response),
      (error: any) => this.handleHealthCheckOrProjectError(projectId, error)
    );

    this.cdr.markForCheck();
  }

  /**
   * Handle successful health check and project detail response
   * @param projectId The project ID that was fetched
   * @param response The API response
   */
  private handleHealthCheckAndProjectSuccess(projectId: string, response: ProjectDetailResponse): void {
    // Clear health check loading state
    this.healthCheckLoadingStates.set(projectId, false);

    // Handle the project detail data - Updated to use project_details structure
    const projectDetail: ProjectDetail = response.project_details;

    // Use the navigation service to navigate to the project
    this.projectNavigationService.navigateToRecentProject(projectId, projectDetail.project_name);

    this.cdr.markForCheck();
  }

  /**
   * Handle health check or project detail API error
   * @param projectId The project ID that failed
   * @param error The error object
   */
  private handleHealthCheckOrProjectError(projectId: string, error: any): void {
    // Clear health check loading state
    this.healthCheckLoadingStates.set(projectId, false);

    // Store error message
    const errorMessage = error?.message || error?.error?.message || 'Failed to load project details';
    this.healthCheckErrors.set(projectId, errorMessage);

    console.error('Failed to perform health check or fetch project details:', error);

    // Show error toast
    this.toastService.error(`Failed to load project details: ${errorMessage}`);

    this.cdr.markForCheck();
  }



  /**
   * Check if a project is currently loading (either health check or project details)
   * @param projectId The project ID to check
   * @returns True if the project is loading
   */
  isProjectLoading(projectId: string): boolean {
    return this.projectDetailLoadingStates.get(projectId) || this.healthCheckLoadingStates.get(projectId) || false;
  }

  /**
   * Check if health check is currently loading for a project
   * @param projectId The project ID to check
   * @returns True if health check is loading
   */
  isHealthCheckLoading(projectId: string): boolean {
    return this.healthCheckLoadingStates.get(projectId) || false;
  }

  /**
   * Get error message for a project (either health check or project details error)
   * @param projectId The project ID to check
   * @returns Error message or null if no error
   */
  getProjectError(projectId: string): string | null {
    return this.projectDetailErrors.get(projectId) || this.healthCheckErrors.get(projectId) || null;
  }

  /**
   * Get health check error message for a project
   * @param projectId The project ID to check
   * @returns Error message or null if no error
   */
  getHealthCheckError(projectId: string): string | null {
    return this.healthCheckErrors.get(projectId) || null;
  }

  getDefaultActionText(type: string): string {
    const actionMap: { [key: string]: string } = {
      ui: 'Generate UI',
      app: 'Generate App',
      analysis: 'Design Analysis',
      accessibility: 'Review Accessibility',
    };
    return actionMap[type.toLowerCase()] || 'View';
  }


  trackByFn(index: number, item: CardOption): string {
    if (!item.id || item.id.trim() === '') {
      return `empty-${index}-${this.currentCategory}`;
    }
    if (item.id.startsWith('placeholder-')) {
      return `${item.id}-${this.currentCategory}`;
    }
    return item.id;
  }

  private setupIntersectionObserver(): void {
    const element = document.querySelector('.recent-creation-wrapper');
    if (element) {
      const observer = this.observerManager.createIntersectionObserver(
        entries => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.loadProjectsIfNeeded();
              observer.disconnect();
            }
          });
        },
        {
          root: null,
          rootMargin: '100px',
          threshold: 0.1,
        }
      );
      observer.observe(element);
    } else {
      this.loadProjectsIfNeeded();
    }
  }

  private initializePlaceholders(): void {
    this.options['recent'] = Array(4)
      .fill(null)
      .map((_, index) => this.createPlaceholder(`recent-${index}`));
    this.options['all'] = Array(12)
      .fill(null)
      .map((_, index) => this.createPlaceholder(`all-${index}`));
  }

  private createPlaceholder(uniqueId: string): CardOption {
    return {
      id: `placeholder-${uniqueId}`,
      heading: '',
      description: '',
      type: 'placeholder',
      timestamp: ''
    };
  }

  /**
   * Load projects only if they haven't been loaded yet and not currently loading
   * This prevents duplicate API calls from multiple triggers
   */
  private loadProjectsIfNeeded(): void {
    if (this.projectsLoaded) {
    }

    if (this.isLoadingProjects) {
      return;
    }

    this.loadProjects();
  }

  private loadProjects(): void {
    this.isLoading = true;
    this.isLoadingProjects = true;
    this.hasError = false;

    // Make only ONE API call to get all projects (12 items)
    // Then derive both 'recent' (4 items) and 'all' (12 items) from the same response
    this.subscriptionManager.subscribe(
      this.recentProjectService.getUserProjects('<EMAIL>', 12),
      response => this.handleProjectsLoad(response),
      (error) => {
        this.isLoading = false;
        this.isLoadingProjects = false;
        this.hasError = true;

        // Show error toast
        const errorMessage = error?.message || error?.error?.message || 'Failed to load projects';
        this.toastService.error(`Failed to load projects: ${errorMessage}`);

        this.cdr.markForCheck();
      }
    );
  }

  private handleProjectsLoad(response: any): void {
    requestAnimationFrame(() => {
      const allProjects = this.mapProjectsToCardOptions(response.projects);

      // Derive both categories from the same API response
      this.options['all'] = allProjects; // All 12 projects
      this.options['recent'] = allProjects.slice(0, 4); // First 4 projects for recent

      // Update flags to indicate projects are loaded
      this.isLoading = false;
      this.isLoadingProjects = false;
      this.projectsLoaded = true;

      this.cdr.markForCheck(); // Trigger change detection after updating data
    });
  }

  private truncateDescription(description: string): string {
    const words = description.split(' ');
    return words.length > 10 ? words.slice(0, 10).join(' ') + '...' : description;
  }

  private mapProjectsToCardOptions(projects: Project[]): CardOption[] {
    // ENHANCED: Filter out projects with null project_type before mapping
    const validProjects = projects.filter(project => {
      const isValid = project.project_type !== null && project.project_type !== undefined;
      if (!isValid) {
      }
      return isValid;
    });

    return validProjects.map(project => ({
      id: project.project_id,
      heading: project.project_name,
      description: this.truncateDescription(project.project_description.replace(/^"|"$/g, '')),
      type: project.project_type!.toLowerCase(), // Safe to use ! since we filtered out nulls
      timestamp: this.recentProjectService.formatDate(project.last_modified),
    }));
  }

  private animateCategorySwitch(category: string): void {
    const gridElement = document.querySelector('.cards-grid') as HTMLElement;
    if (gridElement) {
      gridElement.classList.remove('slide-recent', 'slide-all');
      void gridElement.offsetWidth;
      gridElement.classList.add(category === 'recent' ? 'slide-recent' : 'slide-all');
    }
  }

  /**
   * Handle mouse enter event for project cards
   * @param projectId The project ID being hovered
   */
  onCardMouseEnter(projectId: string): void {
    this.hoveredCardId = projectId;
    this.cdr.markForCheck();
  }

  /**
   * Handle mouse leave event for project cards
   */
  onCardMouseLeave(): void {
    this.hoveredCardId = null;
    this.cdr.markForCheck();
  }

  /**
   * Check if a project card is currently being hovered
   * @param projectId The project ID to check
   * @returns True if the card is hovered
   */
  isCardHovered(projectId: string): boolean {
    return this.hoveredCardId === projectId;
  }

  /**
   * Check if preview is loading for a specific project
   * @param projectId The project ID to check
   * @returns True if preview is loading
   */
  isPreviewLoading(projectId: string): boolean {
    return this.previewLoadingStates.get(projectId) || false;
  }

  /**
   * Handle preview button click
   * @param projectId The project ID to preview
   * @param event The click event
   */
  onPreviewClick(projectId: string, event: Event): void {
    // Prevent event bubbling to avoid triggering card click
    event.stopPropagation();
    event.preventDefault();

    // Don't proceed if already loading
    if (this.isPreviewLoading(projectId)) {
      return;
    }

    // Set loading state
    this.previewLoadingStates.set(projectId, true);
    this.cdr.markForCheck();

    // Make API call to get project details
    this.subscriptionManager.subscribe(
      this.recentProjectService.getProjectById(projectId),
      (response: ProjectDetailResponse) => this.handlePreviewSuccess(projectId, response),
      (error: any) => this.handlePreviewError(projectId, error)
    );
  }

  /**
   * Handle successful preview API response
   * @param projectId The project ID that was fetched
   * @param response The API response
   */
  private handlePreviewSuccess(projectId: string, response: ProjectDetailResponse): void {
    // Clear loading state
    this.previewLoadingStates.set(projectId, false);
    this.cdr.markForCheck();

    // Use the navigation service to navigate to the project - Updated to use project_details structure
    this.projectNavigationService.navigateToRecentProject(projectId, response.project_details.project_name);
  }

  /**
   * Handle preview API error
   * @param projectId The project ID that failed to fetch
   * @param error The error object
   */
  private handlePreviewError(projectId: string, error: any): void {
    // Clear loading state
    this.previewLoadingStates.set(projectId, false);
    this.cdr.markForCheck();

    // Show error toast
    const errorMessage = error?.message || error?.error?.message || 'Failed to load project preview';
    this.toastService.error(`Preview failed: ${errorMessage}`);

    console.error('Failed to fetch project preview:', error);
  }

  /**
   * Check if currently on the landing page (supports both new and legacy routes)
   */
  private isOnLandingPage(): boolean {
    const url = this.router.url;
    return url === '/' ||
           url === '/experience' ||
           url.includes('/experience/main') ||
           url === '/experience/';
  }
}
