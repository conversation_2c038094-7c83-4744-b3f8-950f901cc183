import { Injectable, inject } from '@angular/core';
import { CanActivate, UrlTree, Router, ActivatedRouteSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { PromptSubmissionService } from '../services/prompt-submission.service';
import { NavigationCleanupService } from '../services/navigation-cleanup.service';
import { RouteSessionManagementService } from '../services/route-session-management.service';
import { createLogger } from '../utils';

@Injectable({
  providedIn: 'root'
})
export class PromptSubmissionGuard implements CanActivate {
  private readonly promptSubmissionService = inject(PromptSubmissionService);
  private readonly router = inject(Router);
  private readonly navigationCleanupService = inject(NavigationCleanupService);
  private readonly routeSessionService = inject(RouteSessionManagementService);
  private readonly logger = createLogger('PromptSubmissionGuard');

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const currentUrl = this.router.url;
    const routeData = route.data;

    this.logger.info('🛡️ Prompt submission guard activated', {
      url: currentUrl,
      flowType: routeData?.['flowType'],
      cardType: routeData?.['cardType'],
      skipGuards: routeData?.['skipGuards']
    });

    // Skip guard if explicitly marked to skip
    if (routeData?.['skipGuards']) {
      this.logger.info('⏭️ Skipping prompt submission guard due to route data');
      return true;
    }

    // Skip guard for project loading routes (they don't require prompt submission)
    if (this.routeSessionService.isOnProjectRoute() || currentUrl.includes('/projects/')) {
      this.logger.info('⏭️ Skipping prompt submission guard for project route');
      return true;
    }

    // Check if the user has submitted a prompt
    if (this.promptSubmissionService.hasPromptBeenSubmitted()) {
      this.logger.info('✅ Prompt submission verified - allowing navigation');
      return true; // Allow navigation
    }

    // Check if we're navigating away from a code-preview route
    if (this.isCodePreviewRoute(currentUrl)) {
      this.logger.info('🧹 Navigating away from code-preview route, triggering cleanup');

      // Trigger cleanup when navigating away from code-preview routes
      this.navigationCleanupService.triggerManualCleanup({
        clearSessionStorage: true,
        clearLocalStorage: false,
        clearArtifacts: true,
        clearLogs: true,
        clearPreviewData: true,
        clearUIDesignData: true
      });
    }

    // Get appropriate redirect URL based on current flow
    const redirectUrl = this.getRedirectUrl(route, currentUrl);

    this.logger.warn('❌ Prompt not submitted - redirecting to prompt page', { redirectUrl });
    return this.router.createUrlTree([redirectUrl]);
  }

  /**
   * Get the appropriate redirect URL based on route data and current URL
   */
  private getRedirectUrl(route: ActivatedRouteSnapshot, currentUrl: string): string {
    // First try to use the route data if available
    if (route.data?.['flowType']) {
      const flowType = route.data['flowType'];
      switch (flowType) {
        case 'generate-ui-design':
          return '/generate-ui-design/prompt';
        case 'generate-application':
          return '/generate-application/prompt';
        default:
          return '/';
      }
    }

    // Fallback to URL checking if route data is not available
    if (currentUrl.includes('generate-ui-design')) {
      return '/generate-ui-design/prompt';
    } else if (currentUrl.includes('generate-application')) {
      return '/generate-application/prompt';
    }

    // Use route session service for intelligent redirect
    return this.routeSessionService.getGuardRedirectUrl('prompt-submission');
  }

  /**
   * Check if a route is a code-preview route
   */
  private isCodePreviewRoute(url: string): boolean {
    return url.includes('code-preview');
  }
}
