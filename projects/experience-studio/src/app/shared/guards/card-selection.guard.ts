import { Injectable, inject } from '@angular/core';
import { CanActivate, UrlTree, Router, ActivatedRouteSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { CardSelectionService } from '../services/card-selection.service';
import { RouteSessionManagementService } from '../services/route-session-management.service';
import { createLogger } from '../utils';

@Injectable({
  providedIn: 'root'
})
export class CardSelectionGuard implements CanActivate {
  private readonly cardSelectionService = inject(CardSelectionService);
  private readonly router = inject(Router);
  private readonly routeSessionService = inject(RouteSessionManagementService);
  private readonly logger = createLogger('CardSelectionGuard');

  canActivate(route: ActivatedRouteSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    const currentUrl = this.router.url;
    const routeData = route.data;

    this.logger.info('🛡️ Card selection guard activated', {
      url: currentUrl,
      flowType: routeData?.['flowType'],
      cardType: routeData?.['cardType']
    });

    // Check if the user has selected a card
    if (this.cardSelectionService.hasCardBeenSelected()) {
      this.logger.info('✅ Card selection verified - allowing navigation');
      return true; // Allow navigation
    }

    // If not selected, redirect back to the landing page
    const redirectUrl = this.routeSessionService.getGuardRedirectUrl('card-selection');

    this.logger.warn('❌ Card not selected - redirecting to landing page', { redirectUrl });
    return this.router.createUrlTree([redirectUrl]);
  }
}
