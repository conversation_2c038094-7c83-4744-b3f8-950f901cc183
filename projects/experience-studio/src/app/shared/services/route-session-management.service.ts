import { Injectable, inject, DestroyRef } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { filter, map, distinctUntilChanged, takeUntilDestroyed } from 'rxjs/operators';
import { createLogger } from '../utils';

/**
 * Interface for route session data
 */
export interface RouteSessionData {
  routeHash: string;
  projectId?: string;
  flowType: FlowType;
  cardType?: string;
  timestamp: number;
  routePath: string;
  queryParams: Record<string, any>;
  routeParams: Record<string, any>;
}

/**
 * Flow types for different application flows
 */
export type FlowType = 
  | 'generate-application' 
  | 'generate-ui-design' 
  | 'project-loading' 
  | 'main' 
  | 'legacy';

/**
 * Route session state interface
 */
export interface RouteSessionState {
  currentSession: RouteSessionData | null;
  previousSession: RouteSessionData | null;
  sessionHistory: RouteSessionData[];
}

/**
 * Service for managing route sessions with hashing and state persistence
 * Implements Angular 19+ patterns with inject() and signals
 */
@Injectable({
  providedIn: 'root'
})
export class RouteSessionManagementService {
  private readonly router = inject(Router);
  private readonly activatedRoute = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('RouteSessionManagement');

  // Session storage keys
  private readonly SESSION_STORAGE_KEY = 'route_session_state';
  private readonly MAX_HISTORY_SIZE = 10;

  // Reactive state management
  private readonly sessionState$ = new BehaviorSubject<RouteSessionState>(
    this.loadSessionStateFromStorage()
  );

  // Public observables
  public readonly currentSession$ = this.sessionState$.pipe(
    map(state => state.currentSession),
    distinctUntilChanged((a, b) => a?.routeHash === b?.routeHash)
  );

  public readonly flowType$ = this.currentSession$.pipe(
    map(session => session?.flowType || 'main'),
    distinctUntilChanged()
  );

  public readonly projectId$ = this.currentSession$.pipe(
    map(session => session?.projectId || null),
    distinctUntilChanged()
  );

  constructor() {
    this.initializeRouteTracking();
    this.setupStatePeristence();
  }

  /**
   * Initialize route tracking and session management
   */
  private initializeRouteTracking(): void {
    this.logger.info('🔄 Initializing route session tracking');

    // Track navigation events and update session state
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe((event: NavigationEnd) => {
      this.handleRouteChange(event.url);
    });

    // Initialize with current route if available
    if (this.router.url) {
      this.handleRouteChange(this.router.url);
    }
  }

  /**
   * Setup automatic state persistence
   */
  private setupStatePeristence(): void {
    this.sessionState$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(state => {
      this.saveSessionStateToStorage(state);
    });
  }

  /**
   * Handle route changes and update session state
   */
  private handleRouteChange(url: string): void {
    try {
      const routeData = this.parseRouteData(url);
      const routeHash = this.generateRouteHash(routeData);
      
      const sessionData: RouteSessionData = {
        routeHash,
        projectId: routeData.projectId,
        flowType: routeData.flowType,
        cardType: routeData.cardType,
        timestamp: Date.now(),
        routePath: routeData.routePath,
        queryParams: routeData.queryParams,
        routeParams: routeData.routeParams
      };

      this.updateSessionState(sessionData);
      
      this.logger.info('📍 Route session updated', {
        url,
        hash: routeHash,
        flowType: routeData.flowType,
        projectId: routeData.projectId
      });
    } catch (error) {
      this.logger.error('❌ Failed to handle route change:', error);
    }
  }

  /**
   * Parse route data from URL
   */
  private parseRouteData(url: string): {
    projectId?: string;
    flowType: FlowType;
    cardType?: string;
    routePath: string;
    queryParams: Record<string, any>;
    routeParams: Record<string, any>;
  } {
    const urlTree = this.router.parseUrl(url);
    const segments = urlTree.root.children.primary?.segments || [];
    const queryParams = urlTree.queryParams;
    
    let projectId: string | undefined;
    let flowType: FlowType = 'main';
    let cardType: string | undefined;
    let routeParams: Record<string, any> = {};

    // Parse different route patterns
    if (url.includes('/projects/')) {
      // New project route pattern: /projects/{projectId}
      const projectMatch = url.match(/\/projects\/([^\/\?]+)/);
      if (projectMatch) {
        projectId = projectMatch[1];
        flowType = 'project-loading';
        routeParams.projectId = projectId;
      }
    } else if (url.includes('/generate-application')) {
      flowType = 'generate-application';
      cardType = 'Generate Application';
      
      // Check for nested project routes
      const nestedProjectMatch = url.match(/\/code-preview\/projects\/([^\/\?]+)/);
      if (nestedProjectMatch) {
        projectId = nestedProjectMatch[1];
        routeParams.projectId = projectId;
      }
    } else if (url.includes('/generate-ui-design')) {
      flowType = 'generate-ui-design';
      cardType = 'Generate UI Design';
    } else if (url.includes('/experience')) {
      // Legacy routes
      flowType = 'legacy';
    }

    return {
      projectId,
      flowType,
      cardType,
      routePath: urlTree.root.toString(),
      queryParams,
      routeParams
    };
  }

  /**
   * Generate a unique hash for the route session
   */
  private generateRouteHash(routeData: {
    projectId?: string;
    flowType: FlowType;
    cardType?: string;
    routePath: string;
    queryParams: Record<string, any>;
    routeParams: Record<string, any>;
  }): string {
    const hashInput = JSON.stringify({
      projectId: routeData.projectId,
      flowType: routeData.flowType,
      cardType: routeData.cardType,
      routePath: routeData.routePath,
      queryParams: routeData.queryParams,
      routeParams: routeData.routeParams
    });

    // Simple hash function
    let hash = 0;
    for (let i = 0; i < hashInput.length; i++) {
      const char = hashInput.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return `route_${Math.abs(hash).toString(36)}_${Date.now().toString(36)}`;
  }

  /**
   * Update session state with new session data
   */
  private updateSessionState(newSession: RouteSessionData): void {
    const currentState = this.sessionState$.value;
    
    // Don't update if the route hash is the same
    if (currentState.currentSession?.routeHash === newSession.routeHash) {
      return;
    }

    const updatedState: RouteSessionState = {
      currentSession: newSession,
      previousSession: currentState.currentSession,
      sessionHistory: this.updateSessionHistory(currentState.sessionHistory, newSession)
    };

    this.sessionState$.next(updatedState);
  }

  /**
   * Update session history with size limit
   */
  private updateSessionHistory(
    currentHistory: RouteSessionData[], 
    newSession: RouteSessionData
  ): RouteSessionData[] {
    const updatedHistory = [newSession, ...currentHistory];
    
    // Remove duplicates based on route hash
    const uniqueHistory = updatedHistory.filter((session, index, array) => 
      array.findIndex(s => s.routeHash === session.routeHash) === index
    );

    // Limit history size
    return uniqueHistory.slice(0, this.MAX_HISTORY_SIZE);
  }

  /**
   * Load session state from storage
   */
  private loadSessionStateFromStorage(): RouteSessionState {
    try {
      const stored = sessionStorage.getItem(this.SESSION_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored) as RouteSessionState;
        this.logger.info('📂 Loaded route session state from storage');
        return parsed;
      }
    } catch (error) {
      this.logger.warn('⚠️ Failed to load route session state from storage:', error);
    }

    return {
      currentSession: null,
      previousSession: null,
      sessionHistory: []
    };
  }

  /**
   * Save session state to storage
   */
  private saveSessionStateToStorage(state: RouteSessionState): void {
    try {
      sessionStorage.setItem(this.SESSION_STORAGE_KEY, JSON.stringify(state));
    } catch (error) {
      this.logger.warn('⚠️ Failed to save route session state to storage:', error);
    }
  }

  // Public API methods

  /**
   * Get current session data
   */
  public getCurrentSession(): RouteSessionData | null {
    return this.sessionState$.value.currentSession;
  }

  /**
   * Get current flow type
   */
  public getCurrentFlowType(): FlowType {
    return this.getCurrentSession()?.flowType || 'main';
  }

  /**
   * Get current project ID
   */
  public getCurrentProjectId(): string | null {
    return this.getCurrentSession()?.projectId || null;
  }

  /**
   * Check if currently on a project route
   */
  public isOnProjectRoute(): boolean {
    const flowType = this.getCurrentFlowType();
    return flowType === 'project-loading' || this.getCurrentProjectId() !== null;
  }

  /**
   * Check if currently on a specific flow type
   */
  public isOnFlowType(flowType: FlowType): boolean {
    return this.getCurrentFlowType() === flowType;
  }

  /**
   * Get session history
   */
  public getSessionHistory(): RouteSessionData[] {
    return this.sessionState$.value.sessionHistory;
  }

  /**
   * Get previous session
   */
  public getPreviousSession(): RouteSessionData | null {
    return this.sessionState$.value.previousSession;
  }

  /**
   * Clear session state
   */
  public clearSessionState(): void {
    const clearedState: RouteSessionState = {
      currentSession: null,
      previousSession: null,
      sessionHistory: []
    };

    this.sessionState$.next(clearedState);
    this.logger.info('🧹 Route session state cleared');
  }

  /**
   * Generate route URL for specific flow and project
   */
  public generateRouteUrl(flowType: FlowType, projectId?: string, additionalPath?: string): string {
    switch (flowType) {
      case 'project-loading':
        return projectId ? `/projects/${projectId}` : '/projects';

      case 'generate-application':
        if (projectId) {
          return `/generate-application/code-preview/projects/${projectId}`;
        }
        return additionalPath ? `/generate-application/${additionalPath}` : '/generate-application';

      case 'generate-ui-design':
        return additionalPath ? `/generate-ui-design/${additionalPath}` : '/generate-ui-design';

      case 'main':
        return '/';

      case 'legacy':
        return '/experience';

      default:
        return '/';
    }
  }

  /**
   * Navigate to specific flow with optional project ID
   */
  public navigateToFlow(flowType: FlowType, projectId?: string, additionalPath?: string): Promise<boolean> {
    const url = this.generateRouteUrl(flowType, projectId, additionalPath);
    this.logger.info(`🧭 Navigating to flow: ${flowType}`, { url, projectId });

    return this.router.navigate([url]);
  }

  /**
   * Check if route requires card selection
   */
  public routeRequiresCardSelection(url?: string): boolean {
    const targetUrl = url || this.router.url;
    return targetUrl.includes('/prompt') &&
           (targetUrl.includes('/generate-application') || targetUrl.includes('/generate-ui-design'));
  }

  /**
   * Check if route requires prompt submission
   */
  public routeRequiresPromptSubmission(url?: string): boolean {
    const targetUrl = url || this.router.url;
    return targetUrl.includes('/code-preview') &&
           !targetUrl.includes('/projects/'); // Project routes don't require prompt submission
  }

  /**
   * Get appropriate redirect URL for guard failures
   */
  public getGuardRedirectUrl(guardType: 'card-selection' | 'prompt-submission'): string {
    const currentSession = this.getCurrentSession();

    if (guardType === 'card-selection') {
      return '/'; // Redirect to main landing page
    }

    if (guardType === 'prompt-submission') {
      // Redirect to appropriate prompt page based on current flow
      const flowType = currentSession?.flowType;
      switch (flowType) {
        case 'generate-application':
          return '/generate-application/prompt';
        case 'generate-ui-design':
          return '/generate-ui-design/prompt';
        default:
          return '/';
      }
    }

    return '/';
  }
}
