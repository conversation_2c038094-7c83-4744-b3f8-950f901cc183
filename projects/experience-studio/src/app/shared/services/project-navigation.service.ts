import { Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import { ToastService } from './toast.service';
import { RouteSessionManagementService, FlowType } from './route-session-management.service';
import { createLogger } from '../utils';

/**
 * Navigation context for different project flows
 */
export interface ProjectNavigationContext {
  flowType: FlowType;
  source: 'recent-projects' | 'generation' | 'direct';
  cardType?: string;
}

/**
 * Service to handle project navigation with Angular best practices
 * Provides centralized navigation logic for different project flows
 */
@Injectable({
  providedIn: 'root'
})
export class ProjectNavigationService {
  private readonly router = inject(Router);
  private readonly toastService = inject(ToastService);
  private readonly routeSessionService = inject(RouteSessionManagementService);
  private readonly logger = createLogger('ProjectNavigation');

  /**
   * Navigate to project with proper route structure based on context
   * @param projectId The project ID to navigate to
   * @param context Navigation context (flow type, source, etc.)
   * @param projectName Optional project name for toast message
   */
  navigateToProject(
    projectId: string,
    context: ProjectNavigationContext = { flowType: 'project-loading', source: 'recent-projects' },
    projectName?: string
  ): Promise<boolean> {
    if (!projectId) {
      this.toastService.error('Invalid project ID');
      return Promise.resolve(false);
    }

    // Show loading toast with appropriate message
    const loadingMessage = this.getLoadingMessage(context, projectName);
    this.toastService.info(loadingMessage);

    // Generate target route based on context
    const targetRoute = this.generateProjectRoute(projectId, context);

    this.logger.info('🧭 Navigating to project', {
      projectId,
      context,
      targetRoute,
      projectName
    });

    // Navigate to the correct route
    return this.router.navigate(targetRoute)
      .then(success => {
        if (success) {
          this.logger.info('✅ Project navigation successful');
        } else {
          this.logger.error('❌ Project navigation failed');
          this.toastService.error('Failed to navigate to project');
        }
        return success;
      })
      .catch(error => {
        this.logger.error('❌ Project navigation error:', error);
        this.toastService.error('Failed to navigate to project');
        return false;
      });
  }

  /**
   * Navigate to recent project (simplified method for backward compatibility)
   * @param projectId The project ID to navigate to
   * @param projectName Optional project name for toast message
   */
  navigateToRecentProject(projectId: string, projectName?: string): Promise<boolean> {
    return this.navigateToProject(
      projectId,
      { flowType: 'project-loading', source: 'recent-projects' },
      projectName
    );
  }

  /**
   * Navigate to project from generation flow
   * @param projectId The project ID to navigate to
   * @param flowType The generation flow type
   * @param projectName Optional project name for toast message
   */
  navigateToGeneratedProject(
    projectId: string,
    flowType: 'generate-application' | 'generate-ui-design',
    projectName?: string
  ): Promise<boolean> {
    const cardType = flowType === 'generate-application' ? 'Generate Application' : 'Generate UI Design';

    return this.navigateToProject(
      projectId,
      { flowType, source: 'generation', cardType },
      projectName
    );
  }

  /**
   * Generate appropriate route based on project context
   */
  private generateProjectRoute(projectId: string, context: ProjectNavigationContext): string[] {
    switch (context.flowType) {
      case 'project-loading':
        // Direct project route for recent projects
        return ['/projects', projectId];

      case 'generate-application':
        if (context.source === 'generation') {
          // Nested route for generation flow
          return ['/generate-application/code-preview/projects', projectId];
        }
        return ['/projects', projectId];

      case 'generate-ui-design':
        if (context.source === 'generation') {
          // For UI design, redirect to main project route
          return ['/projects', projectId];
        }
        return ['/projects', projectId];

      default:
        return ['/projects', projectId];
    }
  }

  /**
   * Get appropriate loading message based on context
   */
  private getLoadingMessage(context: ProjectNavigationContext, projectName?: string): string {
    const name = projectName ? `"${projectName}"` : 'project';

    switch (context.source) {
      case 'recent-projects':
        return `Opening recent project ${name}...`;
      case 'generation':
        return `Loading generated project ${name}...`;
      case 'direct':
        return `Loading project ${name}...`;
      default:
        return `Loading ${name}...`;
    }
  }

  /**
   * Check if the current route is a project loading route
   * @returns boolean indicating if currently on project loading route
   */
  isOnProjectRoute(): boolean {
    return this.routeSessionService.isOnProjectRoute();
  }

  /**
   * Extract project ID from current route
   * @returns project ID if on project route, null otherwise
   */
  getCurrentProjectId(): string | null {
    return this.routeSessionService.getCurrentProjectId();
  }

  /**
   * Get current flow type
   * @returns current flow type
   */
  getCurrentFlowType(): FlowType {
    return this.routeSessionService.getCurrentFlowType();
  }

  /**
   * Navigate to appropriate flow based on card selection
   * @param cardType The selected card type
   */
  navigateToFlow(cardType: string): Promise<boolean> {
    let flowType: FlowType;
    let targetPath: string;

    switch (cardType) {
      case 'Generate Application':
        flowType = 'generate-application';
        targetPath = '/generate-application/prompt';
        break;
      case 'Generate UI Design':
        flowType = 'generate-ui-design';
        targetPath = '/generate-ui-design/prompt';
        break;
      default:
        flowType = 'main';
        targetPath = '/';
    }

    this.logger.info('🧭 Navigating to flow', { cardType, flowType, targetPath });

    return this.router.navigate([targetPath])
      .then(success => {
        if (success) {
          this.logger.info('✅ Flow navigation successful');
        } else {
          this.logger.error('❌ Flow navigation failed');
        }
        return success;
      })
      .catch(error => {
        this.logger.error('❌ Flow navigation error:', error);
        return false;
      });
  }
}
